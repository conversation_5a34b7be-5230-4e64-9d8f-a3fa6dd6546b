"""
MySQL 连接器模块。

本模块提供了 MySQL 数据库的提取器和加载器实现。
支持异步操作，使用服务器端游标进行流式数据处理，适合处理大规模数据集。
提供了完整的连接管理、查询构建和批量数据加载功能。

主要功能：
- MySQLConnector: 共享的连接管理基类
- MySQLExtractor: 使用服务器端游标的流式数据提取器
- MySQLLoader: 批量数据加载器，支持冲突处理策略
- 支持自定义初始化 SQL 命令
- 支持 REPLACE INTO 和 INSERT IGNORE 策略
"""

import logging
from collections.abc import AsyncIterator
from typing import Any, override

import asyncmy
import asyncmy.cursors  # type: ignore
import pandas as pd
from asyncmy import Connection as MySQLConnection

from src.datax.models import ImportConfig

from ..core import Extractor, Loader, MetadataProvider
from ..models import (
    ColumnInfo,
    ColumnType,
    ConflictStrategy,
    ConstraintInfo,
    ConstraintType,
    ExportConfig,
    IndexInfo,
    IndexType,
    MySQLConfig,
    QueryConfig,
    TableMetadata,
)

logger = logging.getLogger(__name__)


class MySQLConnector:
    """MySQL 提取器和加载器的共享连接逻辑。"""

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 连接器。

        参数:
            config: MySQL 配置对象
        """
        self.config = config
        self.connection: MySQLConnection | None = None

    async def _connect(self) -> None:
        """建立并配置 MySQL 连接。"""
        if self.connection and not self.connection.closed:
            return

        try:
            # 使用配置中的init_sql参数
            init_command = self.config.init_sql if self.config.init_sql else None

            self.connection = await asyncmy.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.get_password_value(),
                database=self.config.database,
                charset="utf8mb4",
                init_command=init_command,
            )
            logger.debug("MySQL connection established.")

        except Exception as e:
            logger.error(f"MySQL connection failed: {e}")
            raise

    async def close(self) -> None:
        """关闭数据库连接。"""
        if self.connection:
            try:
                await self.connection.close()
            except Exception as e:
                logger.error(f"Error closing MySQL connection: {e}")
            finally:
                self.connection = None
                logger.debug("MySQL connection closed.")


class MySQLExtractor(MySQLConnector, Extractor):
    """使用服务器端游标的 MySQL 数据提取器。"""

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 提取器。

        参数:
            config: MySQL 配置对象
        """
        MySQLConnector.__init__(self, config)
        Extractor.__init__(self, config)

    def _build_query(self, query_config: QueryConfig) -> str:
        """
        根据 QueryConfig 模型构建 SQL 查询语句。

        参数:
            query_config: 查询配置对象

        返回:
            str: 构建的 SQL 查询语句
        """
        if query_config.sql_query:
            logger.info("Using provided SQL query.")
            return query_config.sql_query

        cols = (
            ", ".join(f"`{c}`" for c in query_config.columns)
            if query_config.columns
            else "*"
        )
        query = f"SELECT {cols} FROM `{query_config.table}`"
        if query_config.limit:
            query += f" LIMIT {query_config.limit}"
        return query

    @override
    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """
        使用服务器端游标（SSDictCursor）流式提取数据。

        参数:
            query_config: 查询配置对象
            export_config: 导出配置对象

        Yields:
            pd.DataFrame: 数据块
        """
        await self._connect()
        if not self.connection:
            raise ConnectionError("MySQL connection not established.")

        try:
            # 使用服务器端游标进行流式处理
            async with self.connection.cursor(asyncmy.cursors.SSDictCursor) as cursor:
                query = self._build_query(query_config)
                logger.info(f"Executing query with server-side cursor: {query}")
                await cursor.execute(query)

                buffer: list[dict[str, Any]] = []
                while True:
                    # 从服务器端游标获取一小批数据
                    rows = await cursor.fetchmany(size=export_config.cursor_fetch_size)
                    if not rows:
                        break

                    buffer.extend(rows)
                    if len(buffer) >= export_config.parquet_chunk_size:
                        yield pd.DataFrame(buffer)
                        buffer.clear()

                if buffer:
                    yield pd.DataFrame(buffer)
        finally:
            await self.close()


class MySQLLoader(MySQLConnector, Loader):
    """MySQL 数据加载器。"""

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 加载器。

        参数:
            config: MySQL 配置对象
        """
        MySQLConnector.__init__(self, config)
        Loader.__init__(self, config)

    @override
    async def load_chunk(self, data: pd.DataFrame, import_config: ImportConfig) -> None:
        """
        将 DataFrame 数据块加载到 MySQL。

        参数:
            data: 要加载的数据块
            import_config: 导入配置对象
        """
        await self._connect()
        if not self.connection or data.empty:
            return

        # Convert DataFrame to a list of tuples for executemany
        records = data.to_records(index=False).tolist()

        cols = ", ".join(f"`{col}`" for col in data.columns)
        placeholders = ", ".join(["%s"] * len(data.columns))

        if import_config.conflict_strategy == ConflictStrategy.REPLACE:
            # REPLACE INTO is a MySQL-specific extension.
            # Warning: This is a DELETE then INSERT operation, which can have
            # side effects like firing ON DELETE triggers.
            sql = f"REPLACE INTO `{import_config.table_name}` ({cols}) VALUES ({placeholders})"
        else:  # 'ignore'
            sql = f"INSERT IGNORE INTO `{import_config.table_name}` ({cols}) VALUES ({placeholders})"

        async with self.connection.cursor() as cursor:
            try:
                await cursor.executemany(sql, records)
                await self.connection.commit()
                logger.info(
                    f"Loaded {cursor.rowcount} rows into '{import_config.table_name}' using '{import_config.conflict_strategy}' strategy."
                )
            except Exception as e:
                logger.error(f"Error loading data into MySQL: {e}")
                await self.connection.rollback()
                raise


class MySQLMetadataProvider(MySQLConnector, MetadataProvider):
    """MySQL 元数据查询提供者"""

    def __init__(self, config: MySQLConfig):
        """
        初始化 MySQL 元数据查询提供者。

        参数:
            config: MySQL 配置对象
        """
        super().__init__(config)

    def _map_mysql_type_to_column_type(self, mysql_type: str) -> ColumnType:
        """将 MySQL 数据类型映射到标准列类型"""
        mysql_type = mysql_type.lower()

        # 数值类型
        if mysql_type.startswith("int"):
            return ColumnType.INTEGER
        elif mysql_type.startswith("bigint"):
            return ColumnType.BIGINT
        elif mysql_type.startswith("smallint"):
            return ColumnType.SMALLINT
        elif mysql_type.startswith("tinyint"):
            return ColumnType.TINYINT
        elif mysql_type.startswith("decimal") or mysql_type.startswith("numeric"):
            return ColumnType.DECIMAL
        elif mysql_type.startswith("float"):
            return ColumnType.FLOAT
        elif mysql_type.startswith("double"):
            return ColumnType.DOUBLE

        # 字符串类型
        elif mysql_type.startswith("varchar"):
            return ColumnType.VARCHAR
        elif mysql_type.startswith("char"):
            return ColumnType.CHAR
        elif mysql_type == "text":
            return ColumnType.TEXT
        elif mysql_type == "longtext":
            return ColumnType.LONGTEXT
        elif mysql_type == "mediumtext":
            return ColumnType.MEDIUMTEXT

        # 日期时间类型
        elif mysql_type == "date":
            return ColumnType.DATE
        elif mysql_type == "time":
            return ColumnType.TIME
        elif mysql_type == "datetime":
            return ColumnType.DATETIME
        elif mysql_type == "timestamp":
            return ColumnType.TIMESTAMP
        elif mysql_type == "year":
            return ColumnType.YEAR

        # 二进制类型
        elif mysql_type.startswith("binary"):
            return ColumnType.BINARY
        elif mysql_type.startswith("varbinary"):
            return ColumnType.VARBINARY
        elif mysql_type == "blob":
            return ColumnType.BLOB
        elif mysql_type == "longblob":
            return ColumnType.LONGBLOB

        # 其他类型
        elif mysql_type == "json":
            return ColumnType.JSON
        elif mysql_type.startswith("enum"):
            return ColumnType.ENUM
        elif mysql_type.startswith("set"):
            return ColumnType.SET
        elif mysql_type in ("bool", "boolean"):
            return ColumnType.BOOLEAN

        return ColumnType.UNKNOWN

    @override
    async def get_table_metadata(
        self, table_name: str, schema_name: str | None = None
    ) -> TableMetadata:
        """获取 MySQL 表的元数据信息"""
        await self._connect()
        if not self.connection:
            raise RuntimeError("Failed to establish MySQL connection")

        # 检查表是否存在
        if not await self.table_exists(table_name, schema_name):
            raise ValueError(
                f"Table '{table_name}' does not exist in database '{self.config.database}'"
            )

        # 查询列信息
        columns = await self._get_column_info(table_name)

        # 查询索引信息
        indexes = await self._get_index_info(table_name)

        # 查询约束信息
        constraints = await self._get_constraint_info(table_name)

        # 查询表注释
        table_comment = await self._get_table_comment(table_name)

        return TableMetadata(
            table_name=table_name,
            database_name=self.config.database,
            columns=columns,
            indexes=indexes,
            constraints=constraints,
            comment=table_comment,
        )

    async def _get_column_info(self, table_name: str) -> list[ColumnInfo]:
        """查询表的列信息"""
        if not self.connection:
            raise RuntimeError("No database connection")

        query = """
            SELECT
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                COLUMN_KEY,
                EXTRA,
                COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
            ORDER BY ORDINAL_POSITION
        """

        async with self.connection.cursor() as cursor:
            await cursor.execute(query, (self.config.database, table_name))
            rows = await cursor.fetchall()

        columns = []
        for row in rows:
            (
                column_name,
                data_type,
                is_nullable,
                default_value,
                max_length,
                precision,
                scale,
                column_key,
                extra,
                comment,
            ) = row

            columns.append(
                ColumnInfo(
                    name=column_name,
                    data_type=self._map_mysql_type_to_column_type(data_type),
                    is_nullable=is_nullable == "YES",
                    is_primary_key=column_key == "PRI",
                    is_unique=column_key in ("PRI", "UNI"),
                    is_auto_increment=(
                        "auto_increment" in extra.lower() if extra else False
                    ),
                    default_value=default_value,
                    max_length=max_length,
                    precision=precision,
                    scale=scale,
                    comment=comment,
                )
            )

        return columns

    async def _get_index_info(self, table_name: str) -> list[IndexInfo]:
        """查询表的索引信息"""
        if not self.connection:
            raise RuntimeError("No database connection")

        query = """
            SELECT
                INDEX_NAME,
                NON_UNIQUE,
                COLUMN_NAME,
                INDEX_COMMENT
            FROM INFORMATION_SCHEMA.STATISTICS
            WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
            ORDER BY INDEX_NAME, SEQ_IN_INDEX
        """

        async with self.connection.cursor() as cursor:
            await cursor.execute(query, (self.config.database, table_name))
            rows = await cursor.fetchall()

        # 按索引名分组
        index_groups: dict[str, list[tuple]] = {}
        for row in rows:
            index_name, non_unique, column_name, comment = row
            if index_name not in index_groups:
                index_groups[index_name] = []
            index_groups[index_name].append((non_unique, column_name, comment))

        indexes = []
        for index_name, group in index_groups.items():
            non_unique, _, comment = group[0]  # 取第一行的信息
            columns = [col for _, col, _ in group]

            is_unique = non_unique == 0
            is_primary = index_name == "PRIMARY"

            if is_primary:
                index_type = IndexType.PRIMARY
            elif is_unique:
                index_type = IndexType.UNIQUE
            else:
                index_type = IndexType.INDEX

            indexes.append(
                IndexInfo(
                    name=index_name,
                    index_type=index_type,
                    columns=columns,
                    is_unique=is_unique,
                    is_primary=is_primary,
                    comment=comment,
                )
            )

        return indexes

    async def _get_constraint_info(self, table_name: str) -> list[ConstraintInfo]:
        """查询表的约束信息"""
        if not self.connection:
            raise RuntimeError("No database connection")

        constraints = []

        # 查询主键约束
        pk_query = """
            SELECT
                CONSTRAINT_NAME,
                COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
            AND CONSTRAINT_NAME = 'PRIMARY'
            ORDER BY ORDINAL_POSITION
        """

        async with self.connection.cursor() as cursor:
            await cursor.execute(pk_query, (self.config.database, table_name))
            pk_rows = await cursor.fetchall()

        if pk_rows:
            pk_columns = [row[1] for row in pk_rows]
            constraints.append(
                ConstraintInfo(
                    name="PRIMARY",
                    constraint_type=ConstraintType.PRIMARY_KEY,
                    columns=pk_columns,
                )
            )

        # 查询唯一约束
        unique_query = """
            SELECT
                tc.CONSTRAINT_NAME,
                kcu.COLUMN_NAME
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
            JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
                AND tc.TABLE_SCHEMA = kcu.TABLE_SCHEMA
                AND tc.TABLE_NAME = kcu.TABLE_NAME
            WHERE tc.TABLE_SCHEMA = %s AND tc.TABLE_NAME = %s
            AND tc.CONSTRAINT_TYPE = 'UNIQUE'
            ORDER BY tc.CONSTRAINT_NAME, kcu.ORDINAL_POSITION
        """

        async with self.connection.cursor() as cursor:
            await cursor.execute(unique_query, (self.config.database, table_name))
            unique_rows = await cursor.fetchall()

        # 按约束名分组
        unique_groups: dict[str, list[str]] = {}
        for constraint_name, column_name in unique_rows:
            if constraint_name not in unique_groups:
                unique_groups[constraint_name] = []
            unique_groups[constraint_name].append(column_name)

        for constraint_name, columns in unique_groups.items():
            constraints.append(
                ConstraintInfo(
                    name=constraint_name,
                    constraint_type=ConstraintType.UNIQUE,
                    columns=columns,
                )
            )

        # 查询外键约束
        fk_query = """
            SELECT
                kcu.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                ON kcu.CONSTRAINT_NAME = tc.CONSTRAINT_NAME
                AND kcu.TABLE_SCHEMA = tc.TABLE_SCHEMA
                AND kcu.TABLE_NAME = tc.TABLE_NAME
            WHERE kcu.TABLE_SCHEMA = %s AND kcu.TABLE_NAME = %s
            AND tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
            ORDER BY kcu.CONSTRAINT_NAME, kcu.ORDINAL_POSITION
        """

        async with self.connection.cursor() as cursor:
            await cursor.execute(fk_query, (self.config.database, table_name))
            fk_rows = await cursor.fetchall()

        # 按约束名分组
        fk_groups: dict[str, tuple[list[str], str, list[str]]] = {}
        for constraint_name, column_name, ref_table, ref_column in fk_rows:
            if constraint_name not in fk_groups:
                fk_groups[constraint_name] = ([], ref_table, [])
            fk_groups[constraint_name][0].append(column_name)
            fk_groups[constraint_name][2].append(ref_column)

        for constraint_name, (columns, ref_table, ref_columns) in fk_groups.items():
            constraints.append(
                ConstraintInfo(
                    name=constraint_name,
                    constraint_type=ConstraintType.FOREIGN_KEY,
                    columns=columns,
                    referenced_table=ref_table,
                    referenced_columns=ref_columns,
                )
            )

        return constraints

    async def _get_table_comment(self, table_name: str) -> str | None:
        """查询表注释"""
        if not self.connection:
            raise RuntimeError("No database connection")

        query = """
            SELECT TABLE_COMMENT
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
        """

        async with self.connection.cursor() as cursor:
            await cursor.execute(query, (self.config.database, table_name))
            row = await cursor.fetchone()

        return row[0] if row and row[0] else None

    @override
    async def table_exists(
        self, table_name: str, schema_name: str | None = None
    ) -> bool:
        """检查表是否存在"""
        await self._connect()
        if not self.connection:
            return False

        query = """
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
        """

        async with self.connection.cursor() as cursor:
            await cursor.execute(query, (self.config.database, table_name))
            row = await cursor.fetchone()

        return row[0] > 0 if row else False

    @override
    async def get_database_name(self) -> str:
        """获取当前连接的数据库名称"""
        return self.config.database
